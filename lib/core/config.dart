class Config {
  Config._();

  // 🔗 Base URLs
  static const String baseUrl = 'https://yourdomain.com/';
  static const String baseUrlApi = 'https://yourdomain.com/api/';

  // 🔐 Auth & User
  static const String regiseruser = 'auth/register';
  static const String userLogin = 'auth/login';
  static const String forgetPassword = 'auth/forgot-password';
  static const String socialLogin = 'auth/social-login';
  static const String mobileCheck = 'auth/mobile-check';
  static const String accDelete = 'user/delete';
  static const String userInfo = 'user/info';
  static const String identifyapi = 'user/identify';

  // 👤 Profile
  static const String profileInfo = 'profile/info';
  static const String editProfile = 'profile/edit';
  static const String profileView = 'profile/view';
  static const String pro_pic = 'user/profile-pic';

  // 💬 Interaction & Match
  static const String likeDislike = 'user/like-dislike';
  static const String delUnlike = 'user/unlike';
  static const String passed = 'user/passed';
  static const String likeMe = 'user/like-me';
  static const String newMatch = 'match/new';
  static const String favourite = 'user/favourite';

  // 🚫 Block/Report
  static const String blocklist = 'user/blocklist';
  static const String getblockapi = 'user/get-block';
  static const String profileblock = 'profile/block';
  static const String unblockapikey = 'user/unblock';
  static const String reportapi = 'user/report';

  // 💸 Wallet & Payment
  static const String walletupapi = 'wallet/update';
  static const String walletreportapi = 'wallet/report';
  static const String packagelistapi = 'package/list';
  static const String packagepurcheaseapi = 'package/purchase';
  static const String coinreportapi = 'coin/report';
  static const String referansearnapi = 'referral/earn';
  static const String payoutlistapi = 'payout/list';
  static const String requestwithdrewapi = 'payout/request';
  static const String paystackapi = 'payment/paystack';
  static const String paymentGateway = 'payment/gateway';
  static const String plan = 'plan/list';
  static const String planPurchase = 'plan/purchase';

  // 🎁 Gifts
  static const String mygiftapi = 'gift/my';
  static const String giftlistapi = 'gift/list';
  static const String giftbuyapi = 'gift/buy';

  // 📍 Location / Map
  static const String mapInfo = 'map/info';

  // 🔔 Notifications
  static const String notificationList = 'notification/list';
  static const String notificationUrl = 'https://fcm.googleapis.com/fcm/send';

  // 📄 Pages & FAQ
  static const String faq = 'page/faq';
  static const String pageList = 'page/list';

  // 📊 Home & Filters
  static const String homeData = 'home/data';
  static const String filter = 'user/filter';

  // 💑 Relationship & Interests
  static const String relationGoalList = 'relation/goals';
  static const String getInterestList = 'interest/list';
  static const String languagelist = 'language/list';
  static const String religionlist = 'religion/list';

  // 📱 SMS & OTP
  static const String smstypeapi = 'sms/type';
  static const String msgotpapi = 'sms/otp';
  static const String twilyootpapi = 'sms/twilio-otp';

  // 🔧 Config Values
  static const String oneSignel = 'YOUR_ONESIGNAL_APP_ID';
  static String firebaseKey = 'YOUR_FIREBASE_KEY';
  static const String projectID = 'YOUR_FIREBASE_PROJECT_ID';

  // 📦 Headers
  static Map<String, String> header = {
    'Content-Type': 'application/json',
  };
}
