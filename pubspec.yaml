name: dating
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.2.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  flutter_bloc: ^9.1.1
  flutter_svg: ^2.0.9
  #  google_maps_flutter: ^2.5.3
  google_maps_flutter: ^2.7.0
  image_picker: ^1.0.7
  firebase_core: ^3.14.0
  firebase_auth: ^5.6.0
  google_sign_in: ^6.2.1
  flutter_facebook_auth: ^6.0.4
  twitter_login: ^4.4.2
  provider: ^6.1.1
  intl_phone_field: ^3.2.0
  dio: ^5.4.0
  pretty_dio_logger: ^1.3.1
  fluttertoast: ^8.2.4
  geolocator: ^14.0.1
  geocoding: ^4.0.0
  shared_preferences: ^2.2.2
  otp_text_field: ^1.1.3
  carousel_slider: ^5.1.1
  shimmer: ^3.0.0
  cached_network_image: ^3.3.1
  flutter_localization: ^0.3.2
  lottie: ^3.1.0
  cloud_firestore: ^5.6.9
  firebase_messaging: ^15.2.7
  flutter_local_notifications: ^19.2.1
  intl: ^0.20.2
  agora_rtc_engine: ^6.3.2
  permission_handler: ^12.0.0+1
  razorpay_flutter: ^1.3.5
  package_info_plus: ^8.3.0
  share_plus:
  sign_in_with_apple:
  crypto: ^3.0.1
  flutter_widget_from_html_core:
  camera: ^0.11.1
  #  webview_flutter: ^3.0.4
  #  webview_flutter: ^4.0.4
  webview_flutter: ^4.7.0
  flutter_spinkit: ^5.2.0
  http_auth: ^1.0.4
  get: ^4.6.6
  onesignal_flutter: ^5.3.3
  emoji_picker_flutter: ^4.3.0
  flutter_card_swiper: ^7.0.1
  google_mobile_ads: ^6.0.0
  #  google_mobile_ads: ^5.1.0
  #  flutter_paystack: ^1.0.7
  googleapis_auth: ^2.0.0
  json_annotation: ^4.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/icons/
    - assets/Image/
    - assets/lottie/
    - lang/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Satoshi-Regular
      fonts:
        - asset: assets/fonts/Satoshi-Regular.otf
    - family: Satoshi-Bold
      fonts:
        - asset: assets/fonts/Satoshi-Bold.otf
    - family: Satoshi-Medium
      fonts:
        - asset: assets/fonts/Satoshi-Medium.otf
    - family: Satoshi-Black
      fonts:
        - asset: assets/fonts/Satoshi-Black.otf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
